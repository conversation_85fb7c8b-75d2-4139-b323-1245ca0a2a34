# DroidRun

<div align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="./static/droidrun-dark.png">
    <source media="(prefers-color-scheme: light)" srcset="./static/droidrun.png">
    <img src="./static/droidrun.png" alt="DroidRun Logo" width="full">
  </picture>

  **通过大语言模型代理控制您的 Android 和 iOS 设备**

  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
  [![GitHub stars](https://img.shields.io/github/stars/droidrun/droidrun?style=social)](https://github.com/droidrun/droidrun/stargazers)
  [![Discord](https://img.shields.io/discord/1360219330318696488?color=7289DA&label=Discord&logo=discord&logoColor=white)](https://discord.gg/ZZbKEZZkwK)
  [![文档](https://img.shields.io/badge/Documentation-📕-blue)](https://docs.droidrun.ai)
  [![基准测试](https://img.shields.io/badge/Benchmark-🏅-teal)](https://droidrun.ai/benchmark)
  [![Twitter Follow](https://img.shields.io/twitter/follow/droid_run?style=social)](https://x.com/droid_run)

  <a href="https://www.producthunt.com/products/droidrun-framework-for-mobile-agent?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-droidrun&#0045;framework&#0045;for&#0045;mobile&#0045;ai&#0045;agents" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=983810&theme=light&t=1751740003156" alt="Droidrun Framework for mobile AI Agents" style="width: 200px; height: 43px;" width="200" height="43" /></a>
</div>

---

## 🌟 项目概述

DroidRun 是一个革命性的 Android 和 iOS 自动化框架，它使用大语言模型（LLM）代理来控制移动设备。通过自然语言命令，您可以自动化复杂的移动设备操作，无需编写传统的自动化脚本。

### 🎯 什么是 DroidRun？

DroidRun 将人工智能与移动设备控制相结合，让您能够：
- 使用自然语言描述您想要执行的操作
- AI 自动理解屏幕内容并执行相应的操作
- 支持复杂的多步骤任务规划和执行
- 提供视觉理解和智能推理能力

### 🎯 工作原理

1. **连接设备** - 通过 ADB 或网络连接您的 Android/iOS 设备
2. **安装 Portal** - 在设备上安装 DroidRun Portal 应用提供无障碍服务
3. **自然语言命令** - 使用简单的中文或英文描述您想要执行的操作
4. **AI 理解和执行** - LLM 分析屏幕内容并执行相应的操作
5. **智能反馈** - 提供执行结果和状态反馈

### 🎬 演示视频

1. **群聊总结**：让 DroidRun 为您总结升级的群聊内容。
   [![群聊总结器](https://img.youtube.com/vi/ofEnSUHHxX8/0.jpg)](https://www.youtube.com/watch?v=ofEnSUHHxX8)

2. **旅行搜索助手**：见证 DroidRun 寻找最便宜的住宿并通过 Telegram 与同事分享。
   [![旅行搜索助手](https://img.youtube.com/vi/QgtRaLS3NBM/0.jpg)](https://www.youtube.com/watch?v=QgtRaLS3NBM)

3. **自动化 TikTok 购物**：看看 DroidRun 如何在 TikTok Shop 上寻找 Stanley 杯并通过电子邮件发送产品详情。
   [![TikTok 购物助手](https://img.shields.io/badge/TikTok-购物助手-red)](https://www.youtube.com/watch?v=ol3bivBAmn4)

## ✨ 核心特性

### 🤖 AI 驱动的自动化
- **自然语言控制** - 使用中文或英文描述任务
- **智能推理** - 复杂任务的自动规划和分解
- **上下文理解** - AI 理解当前界面状态和用户意图
- **错误恢复** - 自动处理操作失败并尝试替代方案

### �️ 视觉理解能力
- **屏幕分析** - AI 可以"看到"并理解屏幕内容
- **元素识别** - 自动识别按钮、文本、图像等UI元素
- **布局理解** - 理解界面层次结构和空间关系
- **状态检测** - 检测应用状态和界面变化

### 🔌 多 LLM 支持
- **OpenAI** - GPT-4o, GPT-4 等模型
- **Google Gemini** - Gemini Pro, Gemini Flash 等
- **Anthropic Claude** - Claude 3.5 Sonnet 等
- **本地模型** - 通过 Ollama 支持本地部署
- **其他提供商** - DeepSeek, LiteLLM 等

### 📱 全面的设备控制
- **UI 交互** - 点击、滑动、长按、拖拽等手势
- **文本输入** - 智能文本输入和编辑
- **应用管理** - 启动、切换、安装、卸载应用
- **系统操作** - 访问设置、通知、文件系统等
- **硬件控制** - 音量、亮度、网络等硬件功能

### 🛠️ 开发者友好
- **简单 API** - 直观的 Python API 设计
- **CLI 工具** - 强大的命令行界面
- **调试支持** - 内置调试工具和执行跟踪
- **扩展性** - 易于扩展和自定义

### 🌐 跨平台支持
- **Android 设备** - 完整支持通过 ADB 连接
- **iOS 设备** - 实验性支持通过 WebDriverAgent
- **多设备管理** - 同时控制多个设备
- **网络连接** - 支持 USB 和无线连接

## 💡 示例用例

### 🧪 自动化测试
- **UI 测试** - 自动化移动应用程序的用户界面测试
- **回归测试** - 快速验证应用功能的正确性
- **兼容性测试** - 在多个设备上并行执行测试

### 🔄 日常自动化
- **重复任务** - 自动化移动设备上的重复性操作
- **数据收集** - 从应用中提取和整理信息
- **批量操作** - 批量处理联系人、照片等数据

### 👥 用户辅助
- **引导式工作流程** - 为非技术用户创建简化的操作流程
- **远程协助** - 为技术水平较低的用户提供远程帮助
- **无障碍支持** - 为有特殊需求的用户提供辅助功能

### 🔍 探索和学习
- **应用探索** - 使用自然语言命令探索移动应用
- **功能发现** - 自动发现和测试应用的隐藏功能
- **用户体验研究** - 分析用户交互模式和行为

## � 安装说明

### 基础安装

#### 1. 从 uv 安装（推荐）

```bash
# 安装uv工具（根据对应系统安装）
# Mac/Linux/Windows系统通用方法：（pip安装uv，前提本地系统已经安装了python）
pip install uv -i https://mirrors.aliyun.com/pypi/simple/

# Windows系统方法：（powershell终端命令安装）
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex" 
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Mac/Linux系统方法：（curl命令安装）
curl -sSf https://github.com/astral-sh/uv/releases/latest/download/uv-installer.sh | bash

# 安装指定虚拟环境python版本
uv venv --python 3.10.14
uv init

# 激活虚拟环境 (Windows系统)    Mac/Linux系统为: source .venv/bin/activate
.venv\Scripts\activate

# 安装最新版本
uv add droidrun


```

#### 2. 从 Wheel 包安装

如果您有本地构建的 wheel 包：

```bash
# 安装本地 wheel 包
uv add pip setuptools wheel
uv pip install droidrun-0.3.2-py3-none-any.whl --force-reinstall
uv add llama-index posthog==6.0.2 llama-index-llms-google-genai llama-index-llms-litellm>=0.5.1

# 强制重新安装
uv pip install --force-reinstall droidrun-0.3.2-py3-none-any.whl
```

#### 3. 开发模式安装

```bash
# 克隆仓库
git clone https://github.com/droidrun/droidrun.git
cd droidrun

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 开发模式安装
pip install -e .

# 安装开发依赖
pip install -e ".[dev]"
```

#### 4. 从源码构建安装

```bash
# 克隆仓库
git clone https://github.com/droidrun/droidrun.git
cd droidrun

# 构建 wheel 包
python build_wheel.py

# 安装构建的包
pip install dist/droidrun-0.3.2-py3-none-any.whl
```

### LLM 提供商依赖

根据您要使用的 LLM 提供商，安装相应的依赖包：

```bash
# OpenAI
pip install llama-index-llms-openai

# Google Gemini
pip install llama-index-llms-google-genai

# Anthropic Claude
pip install llama-index-llms-anthropic

# Ollama (本地模型)
pip install llama-index-llms-ollama

# DeepSeek
pip install llama-index-llms-deepseek

# 或者一次性安装所有支持的提供商
pip install llama-index-llms-openai llama-index-llms-google-genai llama-index-llms-anthropic llama-index-llms-ollama llama-index-llms-deepseek
```

### 设备连接设置

#### Android 设备设置

1. **启用开发者选项**：
   - 进入 `设置` > `关于手机`
   - 连续点击 `版本号` 7 次
   - 返回设置主页，找到 `开发者选项`

2. **启用 USB 调试**：
   - 在开发者选项中启用 `USB 调试`
   - 连接设备到电脑时选择 `允许 USB 调试`

3. **安装 Portal APK**：
   ```bash
   # 自动下载并安装最新版本的 Portal APK
   droidrun setup

   # 或者手动指定 APK 文件路径
   droidrun setup --path=/path/to/droidrun-portal.apk
   ```

#### iOS 设备设置（实验性）

iOS 支持需要额外的 WebDriverAgent 配置，请参考高级配置部分。

### 验证安装

运行以下命令验证安装是否成功：

```bash
# 检查版本
droidrun --version

# 列出连接的设备
droidrun devices

# 测试连接
droidrun ping

# 测试基本功能
# Windows PowerShell
$env:OPENAI_API_KEY="your_api_key_here"
droidrun "打开设置应用" --provider OpenAI --model gpt-4o

# Linux/macOS
export OPENAI_API_KEY=your_api_key_here
droidrun "打开设置应用" --provider OpenAI --model gpt-4o
```

## 🚀 快速开始

### CLI 基础使用

#### 1. 设置 API 密钥

**Windows PowerShell:**
```powershell
# OpenAI
$env:OPENAI_API_KEY="your_openai_api_key"

# Google Gemini
$env:GOOGLE_API_KEY="your_google_api_key"

# Anthropic
$env:ANTHROPIC_API_KEY="your_anthropic_api_key"

# DeepSeek
$env:DEEPSEEK_API_KEY="your_deepseek_api_key"
```

**Windows CMD:**
```cmd
# OpenAI
set OPENAI_API_KEY=your_openai_api_key

# Google Gemini
set GOOGLE_API_KEY=your_google_api_key

# Anthropic
set ANTHROPIC_API_KEY=your_anthropic_api_key

# DeepSeek
set DEEPSEEK_API_KEY=your_deepseek_api_key
```

**Linux/macOS:**
```bash
# OpenAI
export OPENAI_API_KEY=your_openai_api_key

# Google Gemini
export GOOGLE_API_KEY=your_google_api_key

# Anthropic
export ANTHROPIC_API_KEY=your_anthropic_api_key

# DeepSeek
export DEEPSEEK_API_KEY=your_deepseek_api_key
```

#### 2. 基本命令示例

```bash
# 设置 Portal APK
droidrun setup

# 测试连接
droidrun ping

# 列出连接的设备
droidrun devices

# 使用自然语言命令控制设备
droidrun "打开设置应用并告诉我安卓版本" --provider OpenAI --model gpt-4o

# 使用不同的 LLM 提供商
droidrun "打开计算器" --provider GoogleGenAI --model gemini-2.5-flash
droidrun "检查通知" --provider Anthropic --model claude-3-5-sonnet-20241022
droidrun "打开相机" --provider Ollama --model qwen2.5vl:3b
```

#### 3. 高级选项

```bash
# 启用视觉理解
droidrun "分析当前界面" --vision

# 启用推理模式
droidrun "复杂任务规划" --reasoning

# 启用调试模式
droidrun "测试任务" --debug

# 保存执行轨迹
droidrun "记录任务" --save-trajectory

# 指定设备
droidrun "任务" --device your-device-serial

# 自定义步骤数
droidrun "复杂任务" --steps 20
```

### Python API 使用

#### 1. 基本示例

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent
from llama_index.llms.openai import OpenAI

async def main():
    # 初始化 LLM
    llm = OpenAI(model="gpt-4o", api_key="your-api-key")

    # 加载 Android 工具
    tools = AdbTools(serial="your-device-serial")  # 或者 None 使用第一个设备

    # 创建智能体
    agent = DroidAgent(
        goal="打开设置应用并检查 Android 版本",
        llm=llm,
        tools=tools,
        max_steps=10,
        vision=True,
        reasoning=True,
        debug=True
    )

    # 执行任务
    result = await agent.run()
    print(f"任务结果: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

#### 2. 高级配置示例

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent, set_language, Language
from llama_index.llms.google_genai import GoogleGenAI

async def advanced_example():
    # 设置语言为中文
    set_language(Language.CHINESE)

    # 初始化 Google Gemini LLM
    llm = GoogleGenAI(
        model="models/gemini-2.5-flash",
        api_key="your-google-api-key",
        temperature=0.2
    )

    # 创建 Android 工具实例
    tools = await AdbTools.create(serial=None)  # 自动选择设备

    # 创建高级智能体
    agent = DroidAgent(
        goal="打开微信，查看最新消息，并总结内容",
        llm=llm,
        tools=tools,
        max_steps=15,
        timeout=1000,
        vision=True,
        reasoning=True,
        reflection=True,
        enable_tracing=True,
        debug=True,
        save_trajectories=True
    )

    # 执行任务
    try:
        result = await agent.run()
        print(f"任务执行成功: {result}")
    except Exception as e:
        print(f"任务执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(advanced_example())
```

## 🏗️ 项目结构

```
droidrun/
├── droidrun/                    # 核心框架代码
│   ├── __init__.py             # 包初始化，导出主要类
│   ├── __main__.py             # 模块入口点
│   ├── agent/                  # AI 智能体实现
│   │   ├── droid/             # DroidAgent 核心实现
│   │   ├── utils/             # LLM 加载器和工具
│   │   ├── context/           # 上下文管理
│   │   └── planner/           # 任务规划器
│   ├── tools/                  # 设备控制工具
│   │   ├── tools.py           # 抽象工具基类
│   │   ├── adb.py             # Android ADB 工具
│   │   └── ios.py             # iOS 控制工具
│   ├── adb/                    # ADB 设备管理
│   │   ├── manager.py         # 设备管理器
│   │   ├── device.py          # 设备抽象
│   │   └── wrapper.py         # ADB 包装器
│   ├── cli/                    # 命令行界面
│   │   ├── main.py            # CLI 主入口
│   │   └── logs.py            # 日志处理
│   ├── config/                 # 配置管理
│   │   ├── __init__.py        # 配置导出
│   │   └── language_config.py # 语言配置
│   ├── telemetry/             # 遥测和跟踪
│   │   ├── tracker.py         # 跟踪器
│   │   └── events.py          # 事件定义
│   └── portal.py              # Portal APK 管理
├── docs/                       # 项目文档
│   ├── DroidRun_使用文档.md    # 中文使用文档
│   └── v3/                    # 版本 3 文档
├── static/                     # 静态资源
│   ├── droidrun.png           # 项目 Logo
│   └── droidrun-dark.png      # 深色主题 Logo
├── build_wheel.py             # 打包构建脚本
├── pyproject.toml             # 项目配置文件
├── pyproject_build.toml       # 打包专用配置
├── setup.py                   # 安装脚本
├── MANIFEST.in                # 打包清单
├── LICENSE                    # MIT 许可证
├── CONTRIBUTING.md            # 贡献指南
└── README.md                  # 项目说明文档
```

### 核心模块说明

- **`droidrun.agent`** - AI 智能体核心，包含 DroidAgent 类和相关工具
- **`droidrun.tools`** - 设备控制工具，提供 Android 和 iOS 设备操作接口
- **`droidrun.adb`** - ADB 设备管理，处理 Android 设备连接和通信
- **`droidrun.cli`** - 命令行界面，提供用户友好的 CLI 工具
- **`droidrun.config`** - 配置管理，包含语言设置和其他配置选项
- **`droidrun.portal`** - Portal APK 管理，处理 Android 端应用的安装和配置

## ⚙️ 配置说明

### LLM 配置

DroidRun 支持多种 LLM 提供商，通过环境变量或代码配置：

#### 环境变量配置

```bash
# OpenAI
export OPENAI_API_KEY=your_openai_api_key
export OPENAI_BASE_URL=https://api.openai.com/v1  # 可选

# Google Gemini
export GOOGLE_API_KEY=your_google_api_key

# Anthropic Claude
export ANTHROPIC_API_KEY=your_anthropic_api_key

# DeepSeek
export DEEPSEEK_API_KEY=your_deepseek_api_key
export DEEPSEEK_BASE_URL=https://api.deepseek.com  # 可选

# 自定义 OpenAI 兼容 API
export OPENAI_API_KEY=your_custom_api_key
export OPENAI_BASE_URL=https://your-custom-endpoint.com/v1
```

#### 代码配置示例

```python
from droidrun import load_llm

# OpenAI
llm = load_llm(
    provider_name="OpenAI",
    model="gpt-4o",
    api_key="your-api-key",
    base_url="https://api.openai.com/v1",  # 可选
    temperature=0.2
)

# Google Gemini
llm = load_llm(
    provider_name="GoogleGenAI",
    model="models/gemini-2.5-flash",
    api_key="your-google-api-key",
    temperature=0.1
)

# Ollama 本地模型
llm = load_llm(
    provider_name="Ollama",
    model="qwen2.5vl:3b",
    base_url="http://localhost:11434"  # 默认 Ollama 地址
)
```

### 语言设置

```python
from droidrun import set_language, get_language, Language, is_chinese, is_english

# 设置语言为中文
set_language(Language.CHINESE)

# 设置语言为英文
set_language(Language.ENGLISH)

# 检查当前语言
current_lang = get_language()
print(f"当前语言: {current_lang}")

# 语言检查
if is_chinese():
    print("当前使用中文模式")
elif is_english():
    print("Currently using English mode")
```

### 设备配置

#### Android 设备配置

```python
from droidrun import AdbTools, DeviceManager

# 指定设备序列号
tools = AdbTools(serial="your-device-serial")

# 自动选择第一个可用设备
tools = await AdbTools.create(serial=None)

# 获取设备管理器
device_manager = DeviceManager()
devices = await device_manager.list_devices()
print(f"可用设备: {devices}")
```

#### iOS 设备配置（实验性）

```python
from droidrun import IOSTools

# iOS 设备需要 WebDriverAgent URL
ios_tools = IOSTools(url="http://localhost:8100")
```

## 📚 API 文档

### DroidAgent 类

`DroidAgent` 是 DroidRun 的核心智能体类，负责执行自然语言任务。

#### 构造函数

```python
DroidAgent(
    goal: str,                    # 任务目标描述
    llm: LLM,                     # LLM 实例
    tools: Tools,                 # 设备控制工具
    max_steps: int = 15,          # 最大执行步骤数
    timeout: int = 1000,          # 超时时间（秒）
    vision: bool = False,         # 是否启用视觉理解
    reasoning: bool = False,      # 是否启用推理模式
    reflection: bool = False,     # 是否启用反思模式
    enable_tracing: bool = False, # 是否启用执行跟踪
    debug: bool = False,          # 是否启用调试模式
    save_trajectories: bool = False # 是否保存执行轨迹
)
```

#### 主要方法

```python
# 执行任务
async def run() -> str:
    """执行智能体任务并返回结果"""
    pass

# 重置智能体状态
def reset() -> None:
    """重置智能体到初始状态"""
    pass
```

#### 使用示例

```python
from droidrun import DroidAgent, AdbTools
from llama_index.llms.openai import OpenAI

# 创建智能体
agent = DroidAgent(
    goal="打开微信，查看最新消息",
    llm=OpenAI(model="gpt-4o", api_key="your-key"),
    tools=AdbTools(serial="device-serial"),
    vision=True,
    reasoning=True,
    debug=True
)

# 执行任务
result = await agent.run()
```

### AdbTools 类

`AdbTools` 提供了与 Android 设备交互的所有工具方法。

#### 构造函数

```python
AdbTools(serial: str)  # 指定设备序列号

# 或者使用类方法自动选择设备
tools = await AdbTools.create(serial=None)
```

#### 核心方法

```python
# 屏幕截图
async def take_screenshot() -> str:
    """获取设备屏幕截图"""
    pass

# 点击操作
async def tap(x: int, y: int) -> str:
    """在指定坐标点击"""
    pass

async def tap_element(element_description: str) -> str:
    """点击描述的元素"""
    pass

async def tap_by_index(index: int) -> str:
    """通过索引点击元素"""
    pass

# 滑动操作
async def swipe(start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 500) -> str:
    """执行滑动手势"""
    pass

async def scroll_up() -> str:
    """向上滚动"""
    pass

async def scroll_down() -> str:
    """向下滚动"""
    pass

# 文本输入
async def input_text(text: str) -> str:
    """输入文本"""
    pass

async def clear_text() -> str:
    """清除文本"""
    pass

# 应用管理
async def launch_app(package_name: str) -> str:
    """启动应用"""
    pass

async def close_app(package_name: str) -> str:
    """关闭应用"""
    pass

async def install_app(apk_path: str) -> str:
    """安装应用"""
    pass

# 系统操作
async def press_back() -> str:
    """按返回键"""
    pass

async def press_home() -> str:
    """按主页键"""
    pass

async def press_recent() -> str:
    """按最近任务键"""
    pass

# 设备信息
async def get_device_info() -> str:
    """获取设备信息"""
    pass

async def get_current_activity() -> str:
    """获取当前活动"""
    pass
```

### DeviceManager 类

`DeviceManager` 负责管理 Android 设备连接。

#### 主要方法

```python
from droidrun import DeviceManager

# 创建设备管理器
manager = DeviceManager()

# 列出所有连接的设备
devices = await manager.list_devices()

# 获取设备实例
device = await manager.get_device(serial="device-serial")

# 连接网络设备
await manager.connect_device("*************:5555")

# 断开设备连接
await manager.disconnect_device("*************:5555")
```

### IOSTools 类（实验性）

`IOSTools` 提供 iOS 设备控制功能（需要 WebDriverAgent）。

#### 构造函数

```python
IOSTools(url: str)  # WebDriverAgent URL
```

#### 基本方法

```python
# 与 AdbTools 类似的接口
async def take_screenshot() -> str
async def tap(x: int, y: int) -> str
async def swipe(start_x: int, start_y: int, end_x: int, end_y: int) -> str
async def input_text(text: str) -> str
# ... 其他方法
```

### 工具加载器

```python
from droidrun import load_llm

# 加载不同的 LLM
llm = load_llm(
    provider_name="OpenAI",      # 提供商名称
    model="gpt-4o",              # 模型名称
    api_key="your-api-key",      # API 密钥
    base_url=None,               # 可选：自定义 API 端点
    temperature=0.2,             # 可选：温度参数
    **kwargs                     # 其他参数
)

# 支持的提供商
# - OpenAI
# - GoogleGenAI
# - Anthropic
# - Ollama
# - DeepSeek
# - LiteLLM
```

## 🛠️ 开发指南

### 开发环境设置

#### 1. 克隆仓库

```bash
git clone https://github.com/droidrun/droidrun.git
cd droidrun
```

#### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate
```

#### 3. 安装开发依赖

```bash
# 安装项目及开发依赖
pip install -e ".[dev]"

# 或者分步安装
pip install -e .
pip install black ruff mypy bandit safety
```

### 代码规范

#### 代码格式化

```bash
# 使用 Black 格式化代码
black droidrun/

# 使用 Ruff 检查和修复代码
ruff check droidrun/ --fix
ruff format droidrun/
```

#### 类型检查

```bash
# 使用 MyPy 进行类型检查
mypy droidrun/
```

#### 安全检查

```bash
# 使用 Bandit 检查安全问题
bandit -r droidrun/

# 使用 Safety 检查依赖漏洞
safety scan

# 或者使用集成脚本
python -m droidrun.tools.security_check
```

### 测试

#### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_agent.py

# 运行带覆盖率的测试
pytest --cov=droidrun tests/
```

#### 编写测试

```python
import pytest
import asyncio
from droidrun import DroidAgent, AdbTools
from unittest.mock import Mock, AsyncMock

@pytest.mark.asyncio
async def test_droid_agent_basic():
    # 模拟 LLM 和工具
    mock_llm = Mock()
    mock_tools = AsyncMock(spec=AdbTools)

    # 创建智能体
    agent = DroidAgent(
        goal="测试任务",
        llm=mock_llm,
        tools=mock_tools
    )

    # 测试基本功能
    assert agent.goal == "测试任务"
    assert agent.llm == mock_llm
    assert agent.tools == mock_tools
```

### 打包和发布

#### 构建包

```bash
# 使用自定义构建脚本
python build_wheel.py

# 或者使用标准工具
python -m build

# 检查构建的包
python check_package.py
```

#### 发布到 PyPI

```bash
# 安装发布工具
pip install twine

# 上传到测试 PyPI
twine upload --repository testpypi dist/*

# 上传到正式 PyPI
twine upload dist/*
```

### 贡献流程

#### 1. Fork 和克隆

```bash
# Fork 项目到您的 GitHub 账户
# 然后克隆您的 fork
git clone https://github.com/YOUR_USERNAME/droidrun.git
cd droidrun
```

#### 2. 创建功能分支

```bash
git checkout -b feature/your-feature-name
```

#### 3. 开发和测试

```bash
# 进行您的更改
# 运行测试确保一切正常
pytest
black droidrun/
ruff check droidrun/
mypy droidrun/
```

#### 4. 提交更改

```bash
git add .
git commit -m "feat: add your feature description"
```

#### 5. 推送和创建 PR

```bash
git push origin feature/your-feature-name
# 然后在 GitHub 上创建 Pull Request
```

### 文档更新

#### 更新 README

- 如果您更改了功能，请更新 README.md
- 确保代码示例是最新的
- 添加新功能的使用说明

#### 更新 API 文档

- 为新函数和类添加详细的文档字符串
- 使用 Google 风格的文档字符串格式
- 包含参数说明、返回值和使用示例

```python
async def new_function(param1: str, param2: int = 10) -> str:
    """
    新功能的简短描述。

    更详细的功能说明，包括使用场景和注意事项。

    Args:
        param1: 第一个参数的描述
        param2: 第二个参数的描述，默认值为 10

    Returns:
        返回值的描述

    Raises:
        ValueError: 当参数无效时抛出

    Example:
        >>> result = await new_function("test", 20)
        >>> print(result)
        "处理结果"
    """
    pass
```

### 调试技巧

#### 启用调试模式

```python
# 在代码中启用调试
agent = DroidAgent(
    goal="任务",
    llm=llm,
    tools=tools,
    debug=True  # 启用详细日志
)

# 或者在 CLI 中
droidrun "任务" --debug
```

#### 使用执行跟踪

```python
# 启用 Arize Phoenix 跟踪
agent = DroidAgent(
    goal="任务",
    llm=llm,
    tools=tools,
    enable_tracing=True
)
```

#### 保存执行轨迹

```python
# 保存详细的执行轨迹
agent = DroidAgent(
    goal="任务",
    llm=llm,
    tools=tools,
    save_trajectories=True
)
```

## 📄 许可证和贡献

### 许可证

本项目采用 **MIT 许可证**。这意味着您可以：

- ✅ **商业使用** - 在商业项目中使用
- ✅ **修改** - 修改源代码
- ✅ **分发** - 分发原始或修改版本
- ✅ **私人使用** - 在私人项目中使用
- ✅ **专利使用** - 获得专利使用权

**条件**：
- 📋 **许可证和版权声明** - 必须包含原始许可证和版权声明
- 🚫 **责任** - 作者不承担任何责任

详细许可证内容请参阅 [LICENSE](LICENSE) 文件。

### 贡献指南

我们欢迎所有形式的贡献！无论您是：

#### 🐛 报告问题
- 在 [GitHub Issues](https://github.com/droidrun/droidrun/issues) 中报告 bug
- 提供详细的重现步骤和环境信息
- 包含错误日志和截图（如适用）

#### 💡 功能请求
- 在 Issues 中描述您希望的新功能
- 解释使用场景和预期收益
- 讨论实现方案的可行性

#### 📝 改进文档
- 修正文档中的错误或不清楚的地方
- 添加更多使用示例和教程
- 翻译文档到其他语言

#### 🔧 代码贡献
- 修复 bug 和改进性能
- 添加新功能和增强现有功能
- 改进测试覆盖率

#### 贡献步骤

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 fork 项目
   git clone https://github.com/YOUR_USERNAME/droidrun.git
   cd droidrun
   ```

2. **设置开发环境**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   pip install -e ".[dev]"
   ```

3. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

4. **进行更改**
   - 遵循代码规范（Black + Ruff）
   - 添加适当的测试
   - 更新相关文档

5. **运行测试**
   ```bash
   pytest
   black droidrun/
   ruff check droidrun/
   mypy droidrun/
   bandit -r droidrun/
   ```

6. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

7. **推送并创建 PR**
   ```bash
   git push origin feature/your-feature-name
   # 在 GitHub 上创建 Pull Request
   ```

#### 代码规范

- **Python 版本**: 支持 Python 3.10+
- **代码格式**: 使用 Black 和 Ruff
- **类型提示**: 使用 MyPy 进行类型检查
- **文档字符串**: 使用 Google 风格
- **测试**: 使用 pytest，保持高覆盖率
- **安全**: 使用 Bandit 和 Safety 检查

#### 提交消息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构代码
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(agent): add multi-device support
fix(tools): resolve screenshot timeout issue
docs(readme): update installation instructions
```

### 社区

#### 💬 讨论和支持
- **Discord**: [加入我们的 Discord 服务器](https://discord.gg/ZZbKEZZkwK)
- **GitHub Discussions**: [项目讨论区](https://github.com/droidrun/droidrun/discussions)
- **GitHub Issues**: [问题跟踪](https://github.com/droidrun/droidrun/issues)

#### 📱 关注我们
- **Twitter/X**: [@droid_run](https://x.com/droid_run)
- **GitHub**: [droidrun/droidrun](https://github.com/droidrun/droidrun)

#### 📚 资源
- **官方文档**: [docs.droidrun.ai](https://docs.droidrun.ai)
- **基准测试**: [droidrun.ai/benchmark](https://droidrun.ai/benchmark)
- **示例项目**: [GitHub Examples](https://github.com/droidrun/examples)

### 致谢

感谢所有为 DroidRun 项目做出贡献的开发者和用户！

特别感谢：
- **核心开发团队** - 项目的主要维护者
- **社区贡献者** - 提供代码、文档和反馈的用户
- **测试用户** - 帮助发现和报告问题的早期用户
- **开源项目** - DroidRun 基于的优秀开源项目

---

## 🚀 快速链接

现在您已经了解了 DroidRun，可以：

- 📖 **阅读文档**: [docs.droidrun.ai](https://docs.droidrun.ai)
- 🎯 **查看基准测试**: [droidrun.ai/benchmark](https://droidrun.ai/benchmark)
- 💬 **加入社区**: [Discord](https://discord.gg/ZZbKEZZkwK)
- 🐛 **报告问题**: [GitHub Issues](https://github.com/droidrun/droidrun/issues)
- ⭐ **给项目点星**: [GitHub](https://github.com/droidrun/droidrun)

### 相关链接

- 🤖 **了解智能体**: [Agent 概念](https://docs.droidrun.ai/v3/concepts/agent)
- 🔌 **LLM 提供商**: [支持的模型](https://docs.droidrun.ai/v3/concepts/models)
- 📱 **Android 控制**: [设备交互](https://docs.droidrun.ai/v3/concepts/android-control)
- 🏗️ **Portal 应用**: [Portal APK 说明](https://docs.droidrun.ai/v3/concepts/portal-app)

---

<div align="center">
  <h3>🌉 DroidRun - 连接 LLM 与移动设备的缺失桥梁</h3>
  <p>让 AI 理解并控制您的移动设备 📱🤖</p>

  [![Star on GitHub](https://img.shields.io/github/stars/droidrun/droidrun?style=social)](https://github.com/droidrun/droidrun)
  [![Follow on Twitter](https://img.shields.io/twitter/follow/droid_run?style=social)](https://x.com/droid_run)
  [![Join Discord](https://img.shields.io/discord/1360219330318696488?color=7289DA&label=Discord&logo=discord&logoColor=white)](https://discord.gg/ZZbKEZZkwK)
</div>